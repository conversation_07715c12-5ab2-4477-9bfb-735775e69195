var express = require('express');
const {
    getUserContextFrmReq,
    convertPasswordToHashInBody,
} = require('../../../api_models/utils/authrizor');
var router = express.Router();
var HttpStatus = require('http-status-codes');
const {
    validateServiceRequestDeletionInputFrBulk,
    translatedInputBatchDataKeysToRequired,
    validateSrvcProviderAssignmentInputFrBulk,
    validateSrvcReqCreationInputFrBulk,
} = require('../../../form_schemas/service_request');
const {
    validateSrvcProviderCreationInputFrBulk,
    translatedInputBatchDataKeysToRequiredFrPrvdr,
    validateSrvcProviderUpdationInputFrBulk,
} = require('../../../form_schemas/service_provider');
const xmlBodyParser = require('../middlewars/xmlToJsonConverter');
// const { validateSrvcProviderAssignmentInputFrBulk } = require('../../../form_schemas/subtasks');

const convertQueryParamsToBody = (req, res, next) => {
    const { srvc_type_id, tms_display_code, ext_order_id } = req.query;

    req.body.batch_data = [
        {
            srvc_type_id: parseInt(srvc_type_id),
            tms_display_code,
            ext_order_id,
        },
    ];

    next();
};

const deleteServiceRequestsBatchMiddleware = (req, res, next) => {
    const brand_model = setParamsToModel(req);
    if (req.body.batch_data) {
        translatedInputBatchDataKeysToRequired(req.body, req.body.batch_data);
        brand_model
            .deleteServiceRequestsBatch(req.body)
            .then((operationResp) => {
                res.status(operationResp.httpStatus).send(operationResp.resp);
            });

        return;
    } else {
        res.status(HttpStatus.StatusCodes.BAD_REQUEST).send(
            'batch_data expected'
        );
    }
};

const validateSrvcReqDeletionInputBatchData = (req, res, next) => {
    validateServiceRequestDeletionInputFrBulk(req, res, async () => {
        try {
            const brand_model = setParamsToModel(req);
            const operationResp =
                await brand_model.validateSrvcReqsDeletionDetails(req, res);
            if (!operationResp.success) {
                return res
                    .status(operationResp.httpStatus)
                    .send(operationResp.resp);
            }
            next();
        } catch (error) {
            return res
                .status(HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR)
                .send({
                    status: false,
                    message: error,
                    data: {},
                });
        }
    });
};

const checkMandatoryFields = (req, res, next) => {
    let service_type_id =
        req.query.service_type_id || req.params.service_type_id;
    if (service_type_id) {
        return next();
    }
    res.status(HttpStatus.StatusCodes.BAD_REQUEST).send(
        'Missing mandatory fields!'
    );
};

const validateSrvcReqCreationInputBatchData = (req, res, next) => {
    const brand_model = setParamsToModel(req, req.params.service_type_id);
    validateSrvcReqCreationInputFrBulk(req, res, next, brand_model);
};

const parseXmlData = (req, res, next) => {
    const brand_model = setParamsToModel(req, req.params.service_type_id);
    xmlBodyParser(req, res, next, brand_model);
};

// Post order(s) details
router.post(
    '/:service_type_id',
    parseXmlData,
    checkMandatoryFields,
    validateSrvcReqCreationInputBatchData,
    function (req, res, next) {
        const brand_model = setParamsToModel(req, req.params.service_type_id);
        if (req.body.batch_data) {
            // res.status(400).send("WIP Rxd data--> " + JSON.stringify(req.body));
            brand_model.createOrUpdateOrdersBatch(req).then((operationResp) => {
                if (!operationResp.success) {
                    console.log(
                        'v1_order :: api :: servicecCreation :: error :: req',
                        req
                    );
                    console.log(
                        'v1_order :: api :: servicecCreation :: error :: body',
                        req.body
                    );

                    res.status(HttpStatus.StatusCodes.BAD_REQUEST).send(
                        operationResp.resp
                    );
                    return;
                }
                res.status(operationResp.httpStatus).send(operationResp.resp);
            });
            return;
        } else {
            console.log(
                'v1_order :: api :: servicecCreation :: error :: req',
                req
            );
            console.log(
                'v1_order :: api :: servicecCreation :: error :: body',
                req.body
            );

            res.status(HttpStatus.StatusCodes.BAD_REQUEST).send(
                'batch_data expected'
            );
        }

        // brand_model.getOrderDetails(req).then(
        //     operationResp => {
        //         res.status(operationResp.httpStatus).send(operationResp.resp);
        //     }
        // );
        // Dummy response
        // let dummyResp = {parmas:(req.params),body:(req.body)}
        // res.send(dummyResp);
        // res.send(getUserContextFrmReq(req));
    }
);

// Delete order(s) details
router.delete(
    '/',
    validateSrvcReqDeletionInputBatchData,
    deleteServiceRequestsBatchMiddleware
);

// Get order(s) details
router.get('/', checkMandatoryFields, function (req, res, next) {
    const brand_model = setParamsToModel(req, req.query.service_type_id);
    brand_model.getOrderDetails(req).then((operationResp) => {
        res.status(operationResp.httpStatus).send(operationResp.resp);
    });
    // Dummy response
    // res.send(getUserContextFrmReq(req));
});

// Get order(s) details
router.get('/search', checkMandatoryFields, function (req, res, next) {
    const brand_model = setParamsToModel(req, req.query.service_type_id);
    brand_model.searchOrderDetails(req).then((operationResp) => {
        res.status(operationResp.httpStatus).send(operationResp.resp);
    });
});

router.delete(
    '/single_batch',
    convertQueryParamsToBody,
    validateSrvcReqDeletionInputBatchData,
    deleteServiceRequestsBatchMiddleware
);
//Validation of Creation of service provider
const validateSrvcProviderCreationInputData = (req, res, next) => {
    validateSrvcProviderCreationInputFrBulk(req, res, () => {
        const brand_model = setParamsToModel(req);
        translatedInputBatchDataKeysToRequiredFrPrvdr(
            req.body,
            req.body.batch_data
        );
        brand_model
            .validateSrvcPrvdrCreationDetails(req.body)
            .then((validationResp) => {
                // console.log("validateSrvcProviderAssignmentInputData validationResp",validationResp);
                if (validationResp.success) {
                    return next();
                }

                res.status(HttpStatus.StatusCodes.BAD_REQUEST).send(
                    validationResp.resp
                );
            });
    });
};
//Validation of Updation of service provider
const validateSrvcProviderUpdationInputData = (req, res, next) => {
    validateSrvcProviderUpdationInputFrBulk(req, res, () => {
        const brand_model = setParamsToModel(req);
        translatedInputBatchDataKeysToRequiredFrPrvdr(
            req.body,
            req.body.batch_data
        );
        brand_model
            .validateSrvcPrvdrUpdationDetails(req.body)
            .then((validationResp) => {
                // console.log("validateSrvcProviderAssignmentInputData validationResp",validationResp);
                if (validationResp.success) {
                    return next();
                }

                res.status(HttpStatus.StatusCodes.BAD_REQUEST).send(
                    validationResp.resp
                );
            });
    });
};
//Validation of Assignment of service provider to an request
const validateSrvcProviderAssignmentInputData = (req, res, next) => {
    validateSrvcProviderAssignmentInputFrBulk(req, res, () => {
        const brand_model = setParamsToModel(req);
        translatedInputBatchDataKeysToRequired(req.body, req.body.batch_data);
        brand_model
            .validateSrvcPrvdrAssignmentDetailsToSrvcReq(req.body)
            .then((validationResp) => {
                // console.log("validateSrvcProviderAssignmentInputData validationResp",validationResp);
                if (validationResp.success) {
                    return next();
                }

                res.status(HttpStatus.StatusCodes.BAD_REQUEST).send(
                    validationResp.resp
                );
            });
    });
};
//Creation of service provider
router.post(
    '/service_prvdr/create',
    validateSrvcProviderCreationInputData,
    convertPasswordToHashInBody,
    function (req, res, next) {
        // setParamsToModel(req,req.params.service_type_id);
        const brand_model = setParamsToModel(req);
        if (req.body.batch_data) {
            // res.status(400).send("WIP Rxd data--> " + JSON.stringify(req.body));
            brand_model
                .createOrUpdateSrvcPrvdrBatch(req.body)
                .then((operationResp) => {
                    if (!operationResp.success) {
                        res.status(HttpStatus.StatusCodes.BAD_REQUEST).send(
                            operationResp.resp
                        );
                        return;
                    }
                    res.status(operationResp.httpStatus).send(
                        operationResp.resp
                    );
                });
            return;
        } else {
            res.status(HttpStatus.StatusCodes.BAD_REQUEST).send(
                'batch_data expected'
            );
        }
    }
);
//Updation of service provider
router.put(
    '/service_prvdr/update',
    validateSrvcProviderUpdationInputData,
    function (req, res, next) {
        // setParamsToModel(req,req.params.service_type_id);
        const brand_model = setParamsToModel(req);
        if (req.body.batch_data) {
            // res.status(400).send("WIP Rxd data--> " + JSON.stringify(req.body));
            brand_model
                .createOrUpdateSrvcPrvdrBatch(req.body)
                .then((operationResp) => {
                    if (!operationResp.success) {
                        res.status(HttpStatus.StatusCodes.BAD_REQUEST).send(
                            operationResp.resp
                        );
                        return;
                    }
                    res.status(operationResp.httpStatus).send(
                        operationResp.resp
                    );
                });
            return;
        } else {
            res.status(HttpStatus.StatusCodes.BAD_REQUEST).send(
                'batch_data expected'
            );
        }
    }
);
//Assignment of service provider to an request
router.put(
    '/service_req',
    validateSrvcProviderAssignmentInputData,
    function (req, res, next) {
        // setParamsToModel(req,req.params.service_type_id);
        const brand_model = setParamsToModel(req);
        if (req.body.batch_data) {
            // res.status(400).send("WIP Rxd data--> " + JSON.stringify(req.body));
            brand_model
                .createOrUpdateSrvcReqBatch(req.body)
                .then((operationResp) => {
                    if (!operationResp.success) {
                        res.status(HttpStatus.StatusCodes.BAD_REQUEST).send(
                            operationResp.resp
                        );
                        return;
                    }
                    res.status(operationResp.httpStatus).send(
                        operationResp.resp
                    );
                });
            return;
        } else {
            res.status(HttpStatus.StatusCodes.BAD_REQUEST).send(
                'batch_data expected'
            );
        }
    }
);

const setParamsToModel = (req, service_type_id) => {
    const brand_model = require('../../../api_models/brand_model');
    brand_model.database = req.app.get('db');
    brand_model.ip_addr = req.ip;
    brand_model.user_agent = req.get('User-Agent');
    brand_model.user_context = getUserContextFrmReq(req);
    brand_model.srvc_type_id = service_type_id;
    return brand_model.getFreshInstance(brand_model);
};

module.exports = router;
