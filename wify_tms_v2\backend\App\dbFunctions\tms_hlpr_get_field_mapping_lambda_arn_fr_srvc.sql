CREATE OR REPLACE FUNCTION public.tms_hlpr_get_field_mapping_lambda_arn_fr_srvc(srvc_type_id_ integer)
 RETURNS text
 LANGUAGE plpgsql
AS $function$
declare
    lambda_arn_ text;
begin
    -- Get the enter_field_mapping_lambda_arn from form_data of cl_cf_service_types table
    select service_types.form_data->>'enter_field_mapping_lambda_arn'
      from cl_cf_service_types as service_types
     where service_types.service_type_id = srvc_type_id_
       and service_types.is_active = true
     limit 1
      into lambda_arn_;
    
    -- Return NULL if lambda_arn_ is null or empty
    if lambda_arn_ is null or lambda_arn_ = '' then
        return null;
    end if;

    return lambda_arn_;
END;
$function$
;
