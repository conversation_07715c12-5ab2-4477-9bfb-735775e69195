const { parseStringPromise } = require('xml2js');
const { callLambdaFn } = require('../../../api_models/utils/lambda_helpers');
const HttpStatus = require('http-status-codes');
const { getUserContextFrmReq } = require('../../../api_models/utils/authrizor');

/**
 * Helper function to create and configure brand model instance
 * @param {Object} req - Express request object
 * @param {string} service_type_id - Service type ID
 * @returns {Object} Configured brand model instance
 */
const createBrandModel = (req, service_type_id) => {
    const brand_model = require('../../../api_models/brand_model');
    brand_model.database = req.app.get('db');
    brand_model.ip_addr = req.ip;
    brand_model.user_agent = req.get('User-Agent');
    brand_model.user_context = getUserContextFrmReq(req);
    brand_model.srvc_type_id = service_type_id;
    return brand_model.getFreshInstance(brand_model);
};

/**
 * Custom body parser for XML content that processes XML before standard body-parser
 * This middleware should be used before the standard body-parser middleware
 *
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
const xmlBodyParser = async (req, res, next) => {
    const contentType = req.get('Content-Type') || '';
    const isXmlRequest =
        contentType.includes('application/xml') ||
        contentType.includes('text/xml') ||
        contentType.includes('xml');

    if (!isXmlRequest) {
        return next();
    }

    console.log('xmlBodyParser :: Detected XML request, processing...');

    try {
        let rawBody = '';
        req.setEncoding('utf8');

        req.on('data', (chunk) => {
            rawBody += chunk;
        });

        req.on('end', async () => {
            try {
                if (!rawBody || rawBody.trim() === '') {
                    return res.status(HttpStatus.StatusCodes.BAD_REQUEST).send({
                        status: false,
                        message: 'Empty XML body received',
                        data: {},
                    });
                }

                // Extract service_type_id from request params
                const serviceTypeId = req.params.service_type_id;
                if (!serviceTypeId) {
                    return res.status(HttpStatus.StatusCodes.BAD_REQUEST).send({
                        status: false,
                        message:
                            'service_type_id is required in request parameters',
                        data: {},
                    });
                }

                // Get lambda ARN for field mapping transformation
                console.log(
                    'xmlBodyParser :: Getting lambda ARN for service_type_id:',
                    serviceTypeId
                );
                const brandModel = createBrandModel(req, serviceTypeId);
                const lambdaArn =
                    await brandModel.getFieldMappingLambdaArn(serviceTypeId);

                if (!lambdaArn) {
                    console.log(
                        'xmlBodyParser :: No lambda ARN found for service_type_id:',
                        serviceTypeId
                    );
                    return res.status(HttpStatus.StatusCodes.BAD_REQUEST).send({
                        status: false,
                        message: `No field mapping lambda ARN configured for service type ${serviceTypeId}`,
                        data: {},
                    });
                }

                console.log('xmlBodyParser :: Found lambda ARN:', lambdaArn);

                // Convert XML to JSON directly using xml2js
                console.log('xmlBodyParser :: Converting XML to JSON...');
                const convertedJson = await parseStringPromise(rawBody, {
                    explicitArray: false,
                });

                console.log(
                    'xmlBodyParser :: Converted JSON result:',
                    JSON.stringify(convertedJson)
                );

                // Call lambda to transform the converted JSON to desired API format
                console.log(
                    'xmlBodyParser :: Calling data transformation lambda...'
                );
                const lambdaParams = {
                    FunctionName: lambdaArn,
                    InvocationType: 'RequestResponse',
                    LogType: 'Tail',
                    Payload: JSON.stringify({
                        convertedData: convertedJson,
                    }),
                };

                const lambdaResponse = await callLambdaFn(lambdaParams);

                if (lambdaResponse.StatusCode !== 200) {
                    console.log(
                        'xmlBodyParser :: Lambda transformation failed:',
                        lambdaResponse
                    );
                    return res
                        .status(HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR)
                        .send({
                            status: false,
                            message: 'Failed to transform XML data',
                            data: {},
                        });
                }

                const lambdaResult = JSON.parse(lambdaResponse.Payload);

                if (lambdaResult.statusCode !== 200) {
                    console.log(
                        'xmlBodyParser :: Data transformation failed:',
                        lambdaResult
                    );
                    return res.status(HttpStatus.StatusCodes.BAD_REQUEST).send({
                        status: false,
                        message: 'Failed to transform data to API format',
                        data: lambdaResult.body
                            ? JSON.parse(lambdaResult.body)
                            : {},
                    });
                }

                // Extract the transformed data from lambda response
                const transformedData = JSON.parse(lambdaResult.body);

                console.log(
                    'xmlBodyParser :: Transformed data result:',
                    JSON.stringify(transformedData)
                );

                // Set the transformed data as the request body
                req.body = {
                    batch_data: [transformedData],
                };

                // Update content-type header to indicate JSON
                req.headers['content-type'] = 'application/json';

                console.log(
                    'xmlBodyParser :: Successfully converted XML to JSON and transformed data'
                );
                next();
            } catch (processingError) {
                console.log(
                    'xmlBodyParser :: XML processing error:',
                    processingError
                );

                // Determine if it's a conversion error or transformation error
                const isConversionError =
                    processingError.message &&
                    (processingError.message.includes('xml') ||
                        processingError.message.includes('parse'));

                return res.status(HttpStatus.StatusCodes.BAD_REQUEST).send({
                    status: false,
                    message: isConversionError
                        ? 'Failed to convert XML to JSON'
                        : 'Failed to process XML data',
                    data: {
                        error: isConversionError
                            ? 'Invalid XML format'
                            : 'Data transformation error',
                        details: processingError.message,
                    },
                });
            }
        });

        req.on('error', (error) => {
            console.log('xmlBodyParser :: Request error:', error);
            return res.status(HttpStatus.StatusCodes.BAD_REQUEST).send({
                status: false,
                message: 'Error reading request body',
                data: {},
            });
        });
    } catch (error) {
        console.log('xmlBodyParser :: Middleware error:', error);
        return res.status(HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR).send({
            status: false,
            message: 'Internal server error during XML processing',
            data: {},
        });
    }
};

module.exports = xmlBodyParser;
