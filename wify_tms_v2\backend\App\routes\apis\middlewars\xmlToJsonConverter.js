const { parseStringPromise } = require('xml2js');
const { callLambdaFn } = require('../../../api_models/utils/lambda_helpers');
const HttpStatus = require('http-status-codes');

/**
 * Custom body parser for XML content that processes XML before standard body-parser
 * This middleware should be used before the standard body-parser middleware
 *
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
const xmlBodyParser = async (req, res, next) => {
    const contentType = req.get('Content-Type') || '';
    const isXmlRequest =
        contentType.includes('application/xml') ||
        contentType.includes('text/xml') ||
        contentType.includes('xml');

    if (!isXmlRequest) {
        return next();
    }

    console.log('xmlBodyParser :: Detected XML request, processing...');

    try {
        let rawBody = '';
        req.setEncoding('utf8');

        req.on('data', (chunk) => {
            rawBody += chunk;
        });

        req.on('end', async () => {
            try {
                if (!rawBody || rawBody.trim() === '') {
                    return res.status(HttpStatus.StatusCodes.BAD_REQUEST).send({
                        status: false,
                        message: 'Empty XML body received',
                        data: {},
                    });
                }

                // Convert XML to JSON directly using xml2js
                console.log('xmlBodyParser :: Converting XML to JSON...');
                const convertedJson = await parseStringPromise(rawBody, {
                    explicitArray: false,
                });

                console.log(
                    'xmlBodyParser :: Converted JSON result:',
                    JSON.stringify(convertedJson)
                );

                // Call lambda to transform the converted JSON to desired API format
                console.log(
                    'xmlBodyParser :: Calling data transformation lambda...'
                );
                const lambdaParams = {
                    FunctionName:
                        'arn:aws:lambda:ap-south-1:194153695972:function:tms_ikea_b2c_json_reformat_fr_srvc_req_creation',
                    InvocationType: 'RequestResponse',
                    LogType: 'Tail',
                    Payload: JSON.stringify({
                        convertedData: convertedJson,
                    }),
                };

                const lambdaResponse = await callLambdaFn(lambdaParams);

                if (lambdaResponse.StatusCode !== 200) {
                    console.log(
                        'xmlBodyParser :: Lambda transformation failed:',
                        lambdaResponse
                    );
                    return res
                        .status(HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR)
                        .send({
                            status: false,
                            message: 'Failed to transform XML data',
                            data: {},
                        });
                }

                const lambdaResult = JSON.parse(lambdaResponse.Payload);

                if (lambdaResult.statusCode !== 200) {
                    console.log(
                        'xmlBodyParser :: Data transformation failed:',
                        lambdaResult
                    );
                    return res.status(HttpStatus.StatusCodes.BAD_REQUEST).send({
                        status: false,
                        message: 'Failed to transform data to API format',
                        data: lambdaResult.body
                            ? JSON.parse(lambdaResult.body)
                            : {},
                    });
                }

                // Extract the transformed data from lambda response
                const transformedData = JSON.parse(lambdaResult.body);

                console.log(
                    'xmlBodyParser :: Transformed data result:',
                    JSON.stringify(transformedData)
                );

                // Set the transformed data as the request body
                req.body = {
                    batch_data: [transformedData],
                };

                // Update content-type header to indicate JSON
                req.headers['content-type'] = 'application/json';

                console.log(
                    'xmlBodyParser :: Successfully converted XML to JSON and transformed data'
                );
                next();
            } catch (processingError) {
                console.log(
                    'xmlBodyParser :: XML processing error:',
                    processingError
                );

                // Determine if it's a conversion error or transformation error
                const isConversionError =
                    processingError.message &&
                    (processingError.message.includes('xml') ||
                        processingError.message.includes('parse'));

                return res.status(HttpStatus.StatusCodes.BAD_REQUEST).send({
                    status: false,
                    message: isConversionError
                        ? 'Failed to convert XML to JSON'
                        : 'Failed to process XML data',
                    data: {
                        error: isConversionError
                            ? 'Invalid XML format'
                            : 'Data transformation error',
                        details: processingError.message,
                    },
                });
            }
        });

        req.on('error', (error) => {
            console.log('xmlBodyParser :: Request error:', error);
            return res.status(HttpStatus.StatusCodes.BAD_REQUEST).send({
                status: false,
                message: 'Error reading request body',
                data: {},
            });
        });
    } catch (error) {
        console.log('xmlBodyParser :: Middleware error:', error);
        return res.status(HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR).send({
            status: false,
            message: 'Internal server error during XML processing',
            data: {},
        });
    }
};

module.exports = xmlBodyParser;
